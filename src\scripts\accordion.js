import { gsap } from "gsap";

// Accordion functionality with smooth animations
export function initAccordion() {
  // Get all accordion items
  const accordionItems = document.querySelectorAll('.accordion-item');


  if (accordionItems.length === 0) {
    return;
  }

  // Initialize all accordions as closed
  accordionItems.forEach((item, index) => {
    const header = item.querySelector('.accordion-header');
    const body = item.querySelector('.accordion-body');
    const icon = item.querySelector('.accordion-icon');

    if (!header || !body || !icon) {
      return;
    }

    // Set initial closed state
    gsap.set(body, {
      height: 0,
      autoAlpha: 0,
      overflow: 'hidden'
    });

    // Set initial icon state (plus for closed)
    icon.className = 'fa-solid fa-plus text-primary-blue accordion-icon';

    // Add click event listener
    header.addEventListener('click', () => {
      toggleAccordion(item, header, body, icon);
    });

  });

  // Function to toggle accordion state
  function toggleAccordion(item, header, body, icon) {
    const isOpen = item.classList.contains('accordion-open');

    if (isOpen) {
      // Close accordion
      closeAccordion(item, header, body, icon);
    } else {
      // Close all other accordions first
      closeAllAccordions();
      // Then open this one
      openAccordion(item, header, body, icon);
    }
  }

  // Function to open accordion
  function openAccordion(item, header, body, icon) {
    const timeline = gsap.timeline();

    // Add open class
    item.classList.add('accordion-open');

    // Change header style to expanded state
    header.classList.remove('border-primary-blue');
    header.classList.add('bg-[#2458A7]', 'text-white');
    header.querySelector('p').classList.add('text-white');

    // Change icon to minus
    icon.className = 'fa-solid fa-minus text-white accordion-icon';

    // Animate body opening
    timeline
      .to(body, {
        height: 'auto',
        autoAlpha: 1,
        duration: 0.25,
        ease: "power2.out"
      })
      .to(body, {
        paddingTop: '1rem',
        duration: 0.15,
        ease: "power2.out"
      }, "-=0.1");

  }

  // Function to close accordion
  function closeAccordion(item, header, body, icon) {
    const timeline = gsap.timeline();

    // Remove open class
    item.classList.remove('accordion-open');

    // Change header style to closed state
    header.classList.add('border-primary-blue');
    header.classList.remove('bg-[#2458A7]', 'text-white');
    header.querySelector('p').classList.remove('text-white');

    // Change icon to plus
    icon.className = 'fa-solid fa-plus text-primary-blue accordion-icon';

    // Animate body closing
    timeline
      .to(body, {
        paddingTop: '0rem',
        duration: 0.1,
        ease: "power2.in"
      })
      .to(body, {
        height: 0,
        autoAlpha: 0,
        duration: 0.2,
        ease: "power2.in"
      });

  }

  // Function to close all accordions
  function closeAllAccordions() {
    accordionItems.forEach(item => {
      const header = item.querySelector('.accordion-header');
      const body = item.querySelector('.accordion-body');
      const icon = item.querySelector('.accordion-icon');

      if (item.classList.contains('accordion-open')) {
        closeAccordion(item, header, body, icon);
      }
    });
  }

}
